import { useMemo, useEffect, useState } from "react";
import { Screen } from "@/app-components/layout/screen";
import { Card2 } from "@/components/custom/cards/Card2";
import { DataTable } from "@/components/custom/tables/Table1";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

//import SettingsModal from "@/components/custom/modals/SettingsModal";
import BaseModal from "@/components/custom/modals/BaseModal";
//import RequestAVehicle from "./vehicle/VehicleRequestForm";

import {
  Notebook,
  MapPin,
  Calendar,
  Clock,
  Car,
  Users,
  TrendingUp,
  Activity,
  CheckCircle,
  XCircle,
  AlertCircle,
  Play,
  RefreshCw,
  Eye,
  Edit,
  Key,
  User,
  Truck,
  StopCircle,
} from "lucide-react";

import { Link, useNavigate } from "react-router-dom";
import {
  useGetSiteVisitsQuery,
  useGetVehicleRequestsQuery,
  useGetVehiclesQuery,
  useGetSpecialBookingsQuery,
  useUpdateSiteVisitMutation,
  useCreateSiteVisitSurveyMutation,
  useGetSiteVisitSurveysQuery,
  SiteVisit,
  logisticsApiSlice,
} from "@/redux/slices/logistics";
import { useDispatch } from "react-redux";
import MultiStepModal, {
  Step,
} from "@/components/custom/modals/MultiStepModal2";
import ConfirmModal from "@/components/custom/modals/ConfirmationModal";
import driverImg from "@/assets/driver.jpg";
import CreateASpecialAssignment from "./CreateSpecialAssignment";
import { useAuthHook } from '@/utils/useAuthHook';
import { useLogisticsPermissions } from '@/hooks/useLogisticsPermissions';
//import LogisticsPermissionDebugger from '@/components/debug/LogisticsPermissionDebugger';
import { format } from "date-fns";
import { toast } from "@/components/custom/Toast/MyToast";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";

export default function LogisticsDash() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user_details } = useAuthHook();
  const {
    canBookVisit,
    canCompleteTrips,
    canCreateVehicleRequest,
    canCreateSpecialAssignment,
  } = useLogisticsPermissions();

  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedVisit, setSelectedVisit] = useState<any>(null);
  const [isCompleteTripsOpen, setIsCompleteTripsOpen] = useState(false);
  const [isRefreshingSurveys, setIsRefreshingSurveys] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<string>("all");
  const [updateSiteVisit] = useUpdateSiteVisitMutation();
  const [createSiteVisitSurvey] = useCreateSiteVisitSurveyMutation();

  const [editData, setEditData] = useState({
    project: "",
    pickup_location: "",
    marketer: "",
    driver: "",
    pickup_date: "",
    pickup_time: "",
    status: "",
    remarks: "",
  });

  useEffect(() => {
    if (selectedVisit) {
      setEditData({
        project: selectedVisit.siteName,
        pickup_location: selectedVisit.pickupLocation,
        marketer: selectedVisit.clients,
        driver: selectedVisit.chauffeur,
        pickup_date: selectedVisit.rawData?.pickup_date || selectedVisit.date,
        pickup_time: selectedVisit.rawData?.pickup_time || selectedVisit.time,
        status: selectedVisit.status,
        remarks: selectedVisit.rawData?.remarks || "",
      });
    }
  }, [selectedVisit]);

  const handleEdit = (row: any) => {
    setSelectedVisit(row);
    setIsEditOpen(true);
  };
  const handleDelete = (row: any) => {
    setSelectedVisit(row);
    setIsDeleteOpen(true);
  };
  const handleSave = () => {
    console.log("Saving visit:", selectedVisit.id, editData);
    setIsEditOpen(false);
  };
  const handleDeleteConfirm = () => {
    console.log("Deleting visit:", selectedVisit.id);
    setIsDeleteOpen(false);
  };

  // Manual refresh function for surveys
  const handleRefreshSurveys = async () => {
    if (isRefreshingSurveys) return; // Prevent multiple simultaneous refreshes

    setIsRefreshingSurveys(true);
    console.log("=== MANUAL REFRESH TRIGGERED ===");

    try {
      // Force cache invalidation
      dispatch(logisticsApiSlice.util.invalidateTags(['Logistics']));

      // Add a small delay to ensure cache is cleared
      await new Promise(resolve => setTimeout(resolve, 300));

      // Refetch both queries
      const [completedResult, driverCompletedResult, surveysResult] = await Promise.all([
        refetchCompletedVisits(),
        refetchDriverCompletedVisits(),
        refetchExistingSurveys()
      ]);

      console.log("Manual refresh - Completed visits:", completedResult.data?.data?.results?.length || 0);
      console.log("Manual refresh - Existing surveys:", surveysResult.data?.data?.results?.length || 0);
      console.log("=== MANUAL REFRESH COMPLETE ===");
      toast.success("🔄 Survey data refreshed!");
    } catch (error) {
      console.error("Failed to refresh survey data:", error);
      toast.error("❌ Failed to refresh survey data");
    } finally {
      setIsRefreshingSurveys(false);
    }
  };

  // ——————————————————
  // API calls for dashboard data
  // ——————————————————
  const {
    data: visitsResp,
    isLoading: visitsLoading,
    isError: visitsError,
  } = useGetSiteVisitsQuery({
    page: 1,
    page_size: 10,
    marketer: user_details?.employee_no || undefined, // Filter by current user
    ordering: '-created_at,-pickup_date,-id', // Descending order (most recent first)
  });

  const { data: vehicleRequestsResp } = useGetVehicleRequestsQuery({
    page: 1,
    page_size: 5,
  });

  const { data: vehiclesResp, isLoading: vehiclesLoading } =
    useGetVehiclesQuery({
      page: 1,
      page_size: 10,
    });

  const { data: specialBookingsResp } = useGetSpecialBookingsQuery({
    page: 1,
    page_size: 5,
    assigned_to: selectedDepartment && selectedDepartment !== "all" ? selectedDepartment : undefined, 
  });

  // Fetch user's site visits (both approved and in progress)
  const {
    data: userVisitsResp,
    isLoading: userVisitsLoading,
  } = useGetSiteVisitsQuery({
    page: 1,
    page_size: 100,
    marketer: user_details?.employee_no || undefined,
    ordering: '-created_at,-pickup_date,-id', // Descending order (most recent first)
  });

  // Fetch site visits where user is assigned as driver
  const {
    data: driverAssignedVisitsResp,
    isLoading: driverAssignedVisitsLoading,
  } = useGetSiteVisitsQuery({
    page: 1,
    page_size: 100,
    driver: user_details?.employee_no || undefined,
    ordering: '-created_at,-pickup_date,-id', // Descending order (most recent first)
  });

  // Fetch site visits that need surveys (only those that are truly completed but not yet surveyed)
  // We'll filter these in the incompleteSurveys logic instead of fetching by status
  const {
    data: completedVisitsResp,
    isLoading: completedVisitsLoading,
    refetch: refetchCompletedVisits,
  } = useGetSiteVisitsQuery({
    page: 1,
    page_size: 100,
    // Don't filter by status here - we'll handle the logic in incompleteSurveys
    marketer: user_details?.employee_no || undefined,
    ordering: '-created_at,-pickup_date,-id', // Descending order (most recent first)
  });

  // Fetch completed visits where user is assigned as driver
  const {
    data: driverCompletedVisitsResp,
    isLoading: driverCompletedVisitsLoading,
    refetch: refetchDriverCompletedVisits,
  } = useGetSiteVisitsQuery({
    page: 1,
    page_size: 100,
    driver: user_details?.employee_no || undefined,
    ordering: '-created_at,-pickup_date,-id', // Descending order (most recent first)
  });

  // Fetch user's self-managed trips (self-drive, own means) where user is marketer
  const {
    data: selfManagedTripsResp,
    isLoading: selfManagedTripsLoading,
    refetch: refetchSelfManagedTrips,
  } = useGetSiteVisitsQuery({
    page: 1,
    page_size: 100,
    marketer: user_details?.employee_no || undefined,
    ordering: '-created_at,-pickup_date,-id', // Descending order (most recent first)
  });



  // Fetch existing surveys to check which visits already have surveys
  const {
    data: existingSurveysResp,
    isLoading: existingSurveysLoading,
    refetch: refetchExistingSurveys,
  } = useGetSiteVisitSurveysQuery({
    page: 1,
    page_size: 1000, // Get all surveys to check against
  });

  // Transform site visits data
  const visits = useMemo(() => {
    if (!visitsResp?.data?.results) return [];
    return visitsResp.data.results.map((v: SiteVisit) => ({
      id: v.id,
      clients: v.marketer,
      siteName: v.project,
      pickupLocation: v.pickup_location,
      chauffeur: v.driver || "Not assigned",
      date: format(new Date(v.pickup_date), "MMM dd, yyyy"),
      time: v.pickup_time,
      status: v.status,
      rawData: v,
    }));
  }, [visitsResp]);

  // Transform vehicle data for stats
  const vehicleStats = useMemo(() => {
    const vehicles = vehiclesResp?.data?.results || [];
    const available = vehicles.filter((v) => v.status === "Available").length;
    const inUse = vehicles.filter((v) => v.status === "In Use").length;
    const maintenance = vehicles.filter(
      (v) => v.status === "Under Maintenance"
    ).length;

    return {
      total: vehicles.length,
      available,
      inUse,
      maintenance,
    };
  }, [vehiclesResp]);

  // Transform vehicle requests for stats
  const requestStats = useMemo(() => {
    const requests = vehicleRequestsResp?.data?.results || [];
    const pending = requests.filter((r) => r.status === "Pending").length;
    const approved = requests.filter((r) => r.status === "Approved").length;
    const completed = requests.filter(
      (r) => r.status === "Trip Completed"
    ).length;

    return {
      total: requests.length,
      pending,
      approved,
      completed,
    };
  }, [vehicleRequestsResp]);

  // Transform special bookings for stats
  const specialBookingStats = useMemo(() => {
    const bookings = specialBookingsResp?.data?.results || [];
    const pending = bookings.filter((b) => b.status === "Pending").length;
    const approved = bookings.filter((b) => b.status === "Approved").length;
    const inProgress = bookings.filter(
      (b) => b.status === "In Progress"
    ).length;

    return {
      total: bookings.length,
      pending,
      approved,
      inProgress,
    };
  }, [specialBookingsResp]);

  // Transform user's site visits (filter for actionable statuses, exclude completed from trip modal)
  // Combine visits where user is marketer OR driver
  const userVisits = useMemo(() => {
    const marketerVisits = userVisitsResp?.data?.results || [];
    const driverVisits = driverAssignedVisitsResp?.data?.results || [];

    console.log('=== USER VISITS DEBUG ===');
    console.log('Marketer visits:', marketerVisits.length);
    console.log('Driver assigned visits:', driverVisits.length);

    // Combine and deduplicate visits (user might be both marketer and driver)
    const allVisitsMap = new Map();

    // Add marketer visits
    marketerVisits.forEach((v: SiteVisit) => {
      allVisitsMap.set(v.id, { ...v, userRole: 'marketer' });
    });

    // Add driver visits (will overwrite if same ID, but that's fine)
    driverVisits.forEach((v: SiteVisit) => {
      const existing = allVisitsMap.get(v.id);
      allVisitsMap.set(v.id, {
        ...v,
        userRole: existing ? 'both' : 'driver'
      });
    });

    const combinedVisits = Array.from(allVisitsMap.values());
    console.log('Combined unique visits:', combinedVisits.length);

    const filteredVisits = combinedVisits
      .filter((v: SiteVisit & { userRole: string }) => ['Approved', 'In Progress'].includes(v.status)) // Exclude 'Completed' from trip modal
      .map((v: SiteVisit & { userRole: string }) => ({
        id: v.id,
        clients: v.marketer,
        siteName: v.project,
        pickupLocation: v.pickup_location,
        chauffeur: v.driver || "Not assigned",
        date: format(new Date(v.pickup_date), "MMM dd, yyyy"),
        time: v.pickup_time,
        status: v.status,
        userRole: v.userRole,
        rawData: v,
      }));

    console.log('Filtered actionable visits:', filteredVisits.length);
    console.log('=== END USER VISITS DEBUG ===');

    return filteredVisits;
  }, [userVisitsResp, driverAssignedVisitsResp]);

  // Transform self-managed trips (self-drive, own means)
  // Only show trips where user is the marketer (creator) - they manage their own self-drive/own means trips
  const selfManagedTrips = useMemo(() => {
    if (!selfManagedTripsResp?.data?.results) return [];

    console.log('=== SELF-MANAGED TRIPS DEBUG ===');
    console.log('Total site visits for user as marketer:', selfManagedTripsResp.data.results.length);

    const allVisits = selfManagedTripsResp.data.results;
    console.log('Sample visits:', allVisits.slice(0, 3).map(v => ({
      id: v.id,
      transport_type: v.transport_type,
      pickup_location: v.pickup_location,
      is_self_drive: v.is_self_drive,
      status: v.status,
      project: v.project,
      marketer: v.marketer
    })));

    const filteredVisits = allVisits.filter((v: SiteVisit) => {
      // Filter for self-managed transport types and actionable statuses
      // Only include trips where user is the marketer (creator) since they manage their own self-drive/own means
      const isSelfManaged = v.transport_type === 'self_drive' ||
                           v.transport_type === 'own_means' ||
                           v.pickup_location === 'Self Drive' ||
                           v.pickup_location === 'Own Means' ||
                           v.is_self_drive;
      const isActionable = ['Approved', 'In Progress'].includes(v.status);

      console.log(`Visit ${v.id}: isSelfManaged=${isSelfManaged}, isActionable=${isActionable}, transport_type=${v.transport_type}, pickup_location=${v.pickup_location}, status=${v.status}, marketer=${v.marketer}`);

      return isSelfManaged && isActionable;
    });

    console.log('Filtered self-managed trips (marketer only):', filteredVisits.length);
    console.log('=== END SELF-MANAGED TRIPS DEBUG ===');

    return filteredVisits.map((v: SiteVisit) => ({
      id: v.id,
      clients: v.marketer,
      siteName: v.project,
      pickupLocation: v.pickup_location,
      transportType: v.transport_type || (v.is_self_drive ? 'self_drive' : 'company_vehicle'),
      date: format(new Date(v.pickup_date), "MMM dd, yyyy"),
      time: v.pickup_time,
      status: v.status,
      rawData: v,
    }));
  }, [selfManagedTripsResp]);

  // Transform completed visits that need review
  // Combine visits where user is marketer OR driver
  const completedVisits = useMemo(() => {
    const marketerCompleted = completedVisitsResp?.data?.results || [];
    const driverCompleted = driverCompletedVisitsResp?.data?.results || [];

    // Combine and deduplicate completed visits
    const allCompletedMap = new Map();

    // Add marketer completed visits
    marketerCompleted.forEach((v: SiteVisit) => {
      allCompletedMap.set(v.id, { ...v, userRole: 'marketer' });
    });

    // Add driver completed visits
    driverCompleted.forEach((v: SiteVisit) => {
      const existing = allCompletedMap.get(v.id);
      allCompletedMap.set(v.id, {
        ...v,
        userRole: existing ? 'both' : 'driver'
      });
    });

    const combinedCompleted = Array.from(allCompletedMap.values());

    return combinedCompleted.map((v: SiteVisit & { userRole: string }) => ({
      id: v.id,
      clients: v.marketer,
      siteName: v.project,
      pickupLocation: v.pickup_location,
      chauffeur: v.driver || "Not assigned",
      date: format(new Date(v.pickup_date), "MMM dd, yyyy"),
      time: v.pickup_time,
      status: v.status,
      userRole: v.userRole,
      rawData: v,
    }));
  }, [completedVisitsResp, driverCompletedVisitsResp]);

  // ——————————————————
  // Enhanced metrics with multiple data sources
  // ——————————————————
  const metrics = useMemo(() => {
    const totalVisits = visits.length;
    const completedCount = visits.filter(
      (v) => v.status === "Completed"
    ).length;
    // const pendingCount = visits.filter((v) => v.status === "Pending").length;
    // const approvedCount = visits.filter((v) => v.status === "Approved").length;

    return [
      {
        title: "Total Site Visits",
        value: totalVisits,
        icon: MapPin,
        iconBg:
          "bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900",
        iconColor: "text-blue-600 dark:text-blue-300",
        positive: true,
        change: `+${totalVisits}`,
        cardBg:
          "bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800",
        description: "Scheduled visits",
        borderColor: "border-blue-200 dark:border-blue-700",
      },
      {
        title: "Vehicle Requests",
        value: requestStats.total,
        icon: Car,
        iconBg:
          "bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-800 dark:to-purple-900",
        iconColor: "text-purple-600 dark:text-purple-300",
        positive: true,
        change: `${requestStats.pending} pending`,
        cardBg:
          "bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900 dark:to-purple-800",
        description: "Total requests",
        borderColor: "border-purple-200 dark:border-purple-700",
      },
      {
        title: "Available Vehicles",
        value: vehicleStats.available,
        icon: CheckCircle,
        iconBg:
          "bg-gradient-to-br from-green-100 to-green-200 dark:from-green-800 dark:to-green-900",
        iconColor: "text-green-600 dark:text-green-300",
        positive: true,
        change: `${vehicleStats.total} total`,
        cardBg:
          "bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900 dark:to-green-800",
        description: "Ready for use",
        borderColor: "border-green-200 dark:border-green-700",
      },
      {
        title: "Special Bookings",
        value: specialBookingStats.total,
        icon: AlertCircle,
        iconBg:
          "bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-800 dark:to-orange-900",
        iconColor: "text-orange-600 dark:text-orange-300",
        positive: true,
        change: `${specialBookingStats.pending} pending`,
        cardBg:
          "bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900 dark:to-orange-800",
        description: "Special assignments",
        borderColor: "border-orange-200 dark:border-orange-700",
      },
      {
        title: "Completed Today",
        value: completedCount,
        icon: Activity,
        iconBg:
          "bg-gradient-to-br from-emerald-100 to-emerald-200 dark:from-emerald-800 dark:to-emerald-900",
        iconColor: "text-emerald-600 dark:text-emerald-300",
        positive: true,
        change: "Today",
        cardBg:
          "bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900 dark:to-emerald-800",
        description: "Finished visits",
        borderColor: "border-emerald-200 dark:border-emerald-700",
      },
    ];
  }, [visits, vehicleStats, requestStats, specialBookingStats]);

  const quickLinks = useMemo(
    () => (
      <div className="flex flex-col sm:flex-row gap-4">
        {canBookVisit && (
          <Link
            to="/book-visit"
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md"
          >
            Book A Visit
          </Link>
        )}
        {canCompleteTrips && (
          <PrimaryButton
            variant="secondary"
            onClick={() => setIsCompleteTripsOpen(true)}
          >
            Complete Trips
          </PrimaryButton>
        )}
        {canCreateVehicleRequest && (
          <PrimaryButton
            variant="outline"
            onClick={() => navigate("/logistics/vehicles#vehicle-requests")}
          >
            Request a Vehicle
          </PrimaryButton>
        )}
      </div>
    ),
    [navigate, canBookVisit, canCompleteTrips, canCreateVehicleRequest]
  );

  const visitColumns = useMemo(
    () => [
      {
        header: "Marketer",
        accessorKey: "clients",
        cell: ({ row }: any) => (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-purple-50 rounded-full flex items-center justify-center">
              <Users className="w-4 h-4 text-purple-600" />
            </div>
            <span className="font-medium text-sm">{row.original.clients}</span>
          </div>
        ),
      },
      {
        header: "Project",
        accessorKey: "siteName",
        cell: ({ row }: any) => (
          <div className="flex items-center space-x-2">
            <MapPin className="w-4 h-4 text-green-600" />
            <span className="font-medium text-sm">{row.original.siteName}</span>
          </div>
        ),
      },
      {
        header: "Pickup Location",
        accessorKey: "pickupLocation",
        cell: ({ row }: any) => (
          <div className="flex items-center space-x-2">
            <MapPin className="w-4 h-4 text-gray-400" />
            <span className="text-sm">{row.original.pickupLocation}</span>
          </div>
        ),
      },
      {
        header: "Driver",
        accessorKey: "chauffeur",
        cell: ({ row }: any) => (
          <div className="flex items-center space-x-2">
            {row.original.chauffeur !== "Not assigned" ? (
              <>
                <div className="w-8 h-8 bg-orange-50 rounded-full flex items-center justify-center">
                  <Car className="w-4 h-4 text-orange-600" />
                </div>
                <span className="text-sm font-medium">
                  {row.original.chauffeur}
                </span>
              </>
            ) : (
              <span className="text-xs text-gray-400 italic">Not assigned</span>
            )}
          </div>
        ),
      },
      {
        header: "Schedule",
        accessorKey: "date",
        cell: ({ row }: any) => (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
              <Calendar className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <div className="font-medium text-sm">{row.original.date}</div>
              <div className="text-xs text-gray-500 flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                {row.original.time}
              </div>
            </div>
          </div>
        ),
      },
      {
        id: "status",
        header: "Status",
        cell: ({ row }: any) => {
          const s: string = row.original.status;
          let variant: any = "default";
          let bgColor = "";

          // Color-coded status badges
          if (["Completed", "Reviewed"].includes(s)) {
            variant = "secondary";
            bgColor = "bg-green-100 text-green-800 border-green-200";
          } else if (["Approved"].includes(s)) {
            variant = "outline";
            bgColor = "bg-blue-100 text-blue-800 border-blue-200";
          } else if (["In Progress"].includes(s)) {
            variant = "outline";
            bgColor = "bg-yellow-100 text-yellow-800 border-yellow-200";
          } else if (["Pending"].includes(s)) {
            variant = "outline";
            bgColor = "bg-orange-100 text-orange-800 border-orange-200";
          } else if (["Cancelled", "Rejected"].includes(s)) {
            variant = "destructive";
            bgColor = "bg-red-100 text-red-800 border-red-200";
          }

          return (
            <Badge variant={variant} className={`text-xs ${bgColor}`}>
              {s}
            </Badge>
          );
        },
      },
      {
        id: "actions",
        header: "Actions",
        cell: ({ row }: any) => (
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(`/logistics/site-visits/${row.original.id}`)}
              className="h-8 w-8 p-0 hover:bg-blue-50"
            >
              <Eye className="h-4 w-4 text-blue-600" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEdit(row.original)}
              className="h-8 w-8 p-0 hover:bg-green-50"
            >
              <Edit className="h-4 w-4 text-green-600" />
            </Button>
          </div>
        ),
      },
    ],
    [navigate]
  );

  // ——————————————————
  // Incomplete surveys (completed visits without surveys)
  // ——————————————————
  const incompleteSurveys = useMemo(() => {
    if ((!completedVisitsResp?.data?.results && !driverCompletedVisitsResp?.data?.results) || !existingSurveysResp?.data?.results) {
      console.log("=== SURVEY DEBUG: Missing data ===");
      console.log("Marketer completed visits data:", !!completedVisitsResp?.data?.results);
      console.log("Driver completed visits data:", !!driverCompletedVisitsResp?.data?.results);
      console.log("Existing surveys data:", !!existingSurveysResp?.data?.results);
      return [];
    }

    // Get list of site visit IDs that already have surveys
    const visitIdsWithSurveys = new Set(
      existingSurveysResp.data.results.map((survey: any) => survey.site_visit)
    );

    // Combine completed visits from both marketer and driver queries
    const marketerCompleted = completedVisitsResp?.data?.results || [];
    const driverCompleted = driverCompletedVisitsResp?.data?.results || [];

    // Combine and deduplicate
    const allCompletedMap = new Map();
    marketerCompleted.forEach((v: SiteVisit) => {
      allCompletedMap.set(v.id, { ...v, userRole: 'marketer' });
    });
    driverCompleted.forEach((v: SiteVisit) => {
      const existing = allCompletedMap.get(v.id);
      allCompletedMap.set(v.id, {
        ...v,
        userRole: existing ? 'both' : 'driver'
      });
    });

    const allCompletedVisits = Array.from(allCompletedMap.values());

    console.log("=== SURVEY DEBUG INFO ===");
    console.log("Marketer completed visits:", marketerCompleted.length);
    console.log("Driver completed visits:", driverCompleted.length);
    console.log("Combined unique completed visits:", allCompletedVisits.length);
    console.log("Total existing surveys:", existingSurveysResp.data.results.length);
    console.log("Visit IDs with surveys:", Array.from(visitIdsWithSurveys));

    // Filter completed visits that don't have surveys yet and are not reviewed
    const visitsWithoutSurveys = allCompletedVisits
      .filter((visit: SiteVisit & { userRole: string }) => {
        const hasCompletedStatus = visit.status === 'Completed';
        const isNotReviewed = visit.status !== 'Reviewed';
        const hasSurvey = visitIdsWithSurveys.has(visit.id);

        console.log(`Visit ${visit.id} (${visit.project}): status="${visit.status}", hasSurvey=${hasSurvey}, userRole=${visit.userRole}, shouldShow=${hasCompletedStatus && isNotReviewed && !hasSurvey}`);

        // Only show visits that are completed but not yet reviewed and don't have surveys
        return hasCompletedStatus && isNotReviewed && !hasSurvey;
      })
      .map((visit: SiteVisit & { userRole: string }) => ({
        id: visit.id,
        title: `${visit.project} Survey`,
        when: format(new Date(visit.pickup_date), "MMM dd, yyyy 'at' h:mm a"),
        clients: 1, // You might want to get actual client count from surveys
        pickup: visit.pickup_location,
        userRole: visit.userRole,
        rawData: visit,
      }));

    console.log("Filtered incomplete surveys count:", visitsWithoutSurveys.length);
    console.log("Final incomplete surveys:", visitsWithoutSurveys);
    console.log("=== END SURVEY DEBUG ===");
    return visitsWithoutSurveys;
  }, [completedVisitsResp, driverCompletedVisitsResp, existingSurveysResp]);

  // ——————————————————
  // Survey modal & answers
  // ——————————————————
  const [isSurveyOpen, setSurveyOpen] = useState(false);
  const [isSubmittingSurvey, setIsSubmittingSurvey] = useState(false);
  const [currentSurvey, setCurrentSurvey] = useState<
    (typeof incompleteSurveys)[0] | null
  >(null);
  const [answers, setAnswers] = useState({
    visitSite: "",
    bookPlot: "",
    amount: "",
    details: "",
  });

  const startSurvey = (s: (typeof incompleteSurveys)[0]) => {
    setCurrentSurvey(s);
    setAnswers({ visitSite: "", bookPlot: "", amount: "", details: "" });
    setSurveyOpen(true);

    // Show helpful notification about survey requirements
    toast.info("📋 Please answer all questions honestly. Amount is only required if the client booked a plot.");
  };
  const handleSubmitSurvey = async () => {
    if (!currentSurvey?.rawData?.id) {
      toast.error("Invalid survey data - no site visit ID");
      return;
    }

    // Validate required fields
    if (!answers.visitSite) {
      toast.error("Please select whether the client visited the site");
      return;
    }

    if (!answers.bookPlot) {
      toast.error("Please select whether the client booked the plot");
      return;
    }

    // Conditional validation based on boolean responses
    const visited = answers.visitSite === "Yes";
    const booked = answers.bookPlot === "Yes";

    // Only require amount and details if client booked the plot
    if (booked && !answers.amount.trim()) {
      toast.error("Please enter the amount reserved since the client booked the plot");
      return;
    }

    // Always require details/notes for context
    if (!answers.details.trim()) {
      toast.error("Please enter plot details and notes");
      return;
    }

    setIsSubmittingSurvey(true);

    try {
      console.log("=== STARTING SURVEY SUBMISSION ===");
      console.log("Submitting survey for site visit:", currentSurvey.rawData.id);
      console.log("Survey answers:", answers);
      console.log("Current survey data:", currentSurvey);

      // Prepare survey data according to backend API specification
      const surveyData = {
        site_visit: currentSurvey.rawData.id,
        visited: visited,
        booked: booked,
        // Only include amount if client booked, otherwise null
        amount_reserved: booked && answers.amount.trim() ? answers.amount : null,
        // Always include plot details for context
        plot_details: answers.details.trim() || null,
        // Include reason for not visiting if they didn't visit
        reason_not_visited: !visited ? (answers.details.trim() || "Client did not visit the site") : null,
        // Include reason for not booking if they didn't book
        reason_not_booked: !booked ? (answers.details.trim() || "Client did not book the plot") : null,
      };

      console.log("Prepared survey data:", surveyData);

      const result = await createSiteVisitSurvey(surveyData).unwrap();
      console.log("Survey submission result:", result);
      console.log("Survey created successfully with ID:", result.id);

      // Update the site visit status to "Reviewed" after successful survey submission
      const updateResult = await updateSiteVisit({
        id: currentSurvey.rawData.id,
        status: 'Reviewed'
      }).unwrap();
      console.log("Site visit status updated to Reviewed:", updateResult);

      toast.success("🎉 Survey submitted successfully!");

      console.log("=== REFETCHING DATA AFTER SURVEY SUBMISSION ===");

      // Force cache invalidation for logistics data
      dispatch(logisticsApiSlice.util.invalidateTags(['Logistics']));

      // Add a small delay before refetching to ensure backend has processed the update
      await new Promise(resolve => setTimeout(resolve, 500));

      // Refetch data to update the surveys list
      const [completedVisitsResult, driverCompletedVisitsResult, existingSurveysResult] = await Promise.all([
        refetchCompletedVisits(),
        refetchDriverCompletedVisits(),
        refetchExistingSurveys()
      ]);

      console.log("Refetch completed visits result:", completedVisitsResult);
      console.log("Refetch existing surveys result:", existingSurveysResult);
      console.log("=== REFETCH COMPLETE ===");

      // Close modal and reset form
      setSurveyOpen(false);
      setAnswers({ visitSite: "", bookPlot: "", amount: "", details: "" });
      setCurrentSurvey(null);

      console.log("Survey modal closed and form reset");

      // Show a follow-up message about the status change
      setTimeout(() => {
        toast.info("✅ Visit status updated to 'Reviewed' - survey complete!");
      }, 1500);

    } catch (error: any) {
      console.error("Failed to submit survey:", error);
      console.error("Error details:", {
        status: error?.status,
        data: error?.data,
        message: error?.message,
        originalError: error?.originalStatus
      });

      let errorMessage = "Failed to submit survey";

      if (error?.data) {
        if (typeof error.data === 'string') {
          errorMessage = error.data;
        } else if (error.data.detail) {
          errorMessage = error.data.detail;
        } else if (error.data.message) {
          errorMessage = error.data.message;
        } else if (error.data.non_field_errors) {
          errorMessage = Array.isArray(error.data.non_field_errors)
            ? error.data.non_field_errors.join(', ')
            : error.data.non_field_errors;
        } else {
          errorMessage = JSON.stringify(error.data);
        }
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast.error(`❌ ${errorMessage}`);
    } finally {
      setIsSubmittingSurvey(false);
    }
  };

  // ——————————————————
  // Modern Survey steps w/ validation
  // ——————————————————
  const surveySteps: Step[] = [
    {
      title: "🏗️ Site Visit Confirmation",
      content: (
        <div className="space-y-6">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <MapPin className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Site Visit Status
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Please confirm if the client(s) visited the project site
            </p>
          </div>

          <div className="space-y-3">
            <Label htmlFor="visitSite" className="text-base font-medium text-gray-700 dark:text-gray-300">
              Did the client(s) visit the site? *
            </Label>
            <Select
              value={answers.visitSite}
              onValueChange={(value: string) =>
                setAnswers((a) => ({ ...a, visitSite: value }))
              }
            >
              <SelectTrigger className="w-full h-12 text-base">
                <SelectValue placeholder="Please select an option" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Yes">✅ Yes, they visited the site</SelectItem>
                <SelectItem value="No">❌ No, they did not visit</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      ),
      validate: () => answers.visitSite !== "",
    },
    {
      title: "💰 Booking & Payment Details",
      content: (
        <div className="space-y-6">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Booking Information
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Please provide details about the plot booking and payment
            </p>
          </div>

          <div className="space-y-6">
            <div className="space-y-3">
              <Label htmlFor="bookPlot" className="text-base font-medium text-gray-700 dark:text-gray-300">
                Did they book the plot? *
              </Label>
              <Select
                value={answers.bookPlot}
                onValueChange={(value: string) =>
                  setAnswers((a) => ({ ...a, bookPlot: value }))
                }
              >
                <SelectTrigger className="w-full h-12 text-base">
                  <SelectValue placeholder="Select booking status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Yes">🎉 Yes, they booked the plot</SelectItem>
                  <SelectItem value="No">❌ No, they did not book</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <Label htmlFor="amount" className="text-base font-medium text-gray-700 dark:text-gray-300">
                Amount Reserved (KES) {answers.bookPlot === "Yes" ? "*" : "(Optional)"}
              </Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">
                  KES
                </span>
                <Input
                  id="amount"
                  type="number"
                  placeholder="0.00"
                  className="pl-12 h-12 text-base"
                  value={answers.amount}
                  onChange={(e) =>
                    setAnswers((a) => ({ ...a, amount: e.target.value }))
                  }
                  disabled={answers.bookPlot === "No"}
                />
              </div>
              <p className="text-sm text-gray-500">
                {answers.bookPlot === "Yes"
                  ? "Enter the reservation amount paid by the client"
                  : "Amount field is disabled since no booking was made"
                }
              </p>
            </div>

            <div className="space-y-3">
              <Label htmlFor="details" className="text-base font-medium text-gray-700 dark:text-gray-300">
                {answers.visitSite === "No" || answers.bookPlot === "No"
                  ? "Reason & Additional Notes *"
                  : "Plot Details & Notes *"
                }
              </Label>
              <Textarea
                id="details"
                placeholder={
                  answers.visitSite === "No"
                    ? "Please explain why the client did not visit the site..."
                    : answers.bookPlot === "No"
                    ? "Please explain why the client did not book the plot and any additional notes..."
                    : "Enter plot details, client preferences, special requirements, or any additional notes..."
                }
                className="min-h-[120px] text-base resize-none"
                value={answers.details}
                onChange={(e) =>
                  setAnswers((a) => ({ ...a, details: e.target.value }))
                }
              />
              <p className="text-sm text-gray-500">
                {answers.visitSite === "No" || answers.bookPlot === "No"
                  ? "Please provide context and reasons for the outcome"
                  : "Include plot number, size, location, client feedback, or any special arrangements"
                }
              </p>
            </div>
          </div>
        </div>
      ),
      validate: () => {
        // Always require booking selection and details
        if (answers.bookPlot === "" || answers.details.trim() === "") {
          return false;
        }
        // Only require amount if client booked the plot
        if (answers.bookPlot === "Yes" && answers.amount.trim() === "") {
          return false;
        }
        return true;
      },
    },
    {
      title: "📋 Review & Submit",
      content: (
        <div className="space-y-6">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <Notebook className="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Survey Summary
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Please review your responses before submitting the survey
            </p>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <MapPin className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Site Visit Status
                    </Label>
                    <p className="text-base font-semibold text-gray-900 dark:text-gray-100">
                      {answers.visitSite === "Yes" ? "✅ Visited" : "❌ Did not visit"}
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Booking Status
                    </Label>
                    <p className="text-base font-semibold text-gray-900 dark:text-gray-100">
                      {answers.bookPlot === "Yes" ? "🎉 Plot Booked" : "❌ Not booked"}
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-yellow-600 dark:text-yellow-400 font-bold text-sm">KES</span>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Amount Reserved
                    </Label>
                    <p className="text-base font-semibold text-gray-900 dark:text-gray-100">
                      KES {answers.amount ? Number(answers.amount).toLocaleString() : '0'}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Notebook className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                </div>
                <div className="flex-1">
                  <Label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Plot Details & Notes
                  </Label>
                  <div className="mt-2 p-3 bg-white dark:bg-gray-900 rounded-md border border-gray-200 dark:border-gray-600">
                    <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                      {answers.details || "No additional details provided"}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className={`border rounded-lg p-4 ${
            isSubmittingSurvey
              ? "bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-700"
              : "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700"
          }`}>
            <div className="flex items-center space-x-2">
              {isSubmittingSurvey ? (
                <>
                  <div className="w-5 h-5 border-2 border-yellow-600 border-t-transparent rounded-full animate-spin" />
                  <p className="text-sm text-yellow-800 dark:text-yellow-200 font-medium">
                    Submitting survey...
                  </p>
                </>
              ) : (
                <>
                  <CheckCircle className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  <p className="text-sm text-blue-800 dark:text-blue-200 font-medium">
                    Ready to submit survey
                  </p>
                </>
              )}
            </div>
            <p className={`text-xs mt-1 ${isSubmittingSurvey ? 'text-yellow-600 dark:text-yellow-300' : 'text-blue-600 dark:text-blue-300'}`}>
              {isSubmittingSurvey
                ? "Please wait while we save your survey responses..."
                : "This information will be saved and cannot be modified after submission."
              }
            </p>
          </div>
        </div>
      ),
    },
  ];

  const handleStartTrip = async (visit: SiteVisit) => {
    try {
      console.log("Starting trip for visit:", visit);
      const result = await updateSiteVisit({
        id: visit.id!,
        status: "In Progress",
      }).unwrap();
      console.log("Trip start result:", result);
      toast.success(`🚗 Trip started for ${visit.project}! Drive safely.`);
      // Don't close modal so user can see the status change and continue with other trips
    } catch (error: any) {
      console.error("Failed to start trip:", error);
      const errorMessage = error?.data?.detail || error?.message || "Failed to start trip";
      toast.error(`❌ Failed to start trip: ${errorMessage}`);
    }
  };

  const handleEndTrip = async (visit: SiteVisit) => {
    try {
      console.log("Ending trip for visit:", visit);
      const result = await updateSiteVisit({
        id: visit.id!,
        status: "Completed",
      }).unwrap();
      console.log("Trip completion result:", result);

      // Show success notification with next steps
      toast.success(`✅ Trip completed for ${visit.project}! Please complete the survey when ready.`);

      // Refetch data to update the UI and remove completed trip from modal
      await Promise.all([
        refetchCompletedVisits(),
        refetchDriverCompletedVisits(),
        refetchExistingSurveys()
      ]);

      // Keep modal open briefly to show the success, then close
      setTimeout(() => {
        setIsCompleteTripsOpen(false);
      }, 2000);

    } catch (error: any) {
      console.error("Failed to complete trip:", error);
      const errorMessage = error?.data?.detail || error?.message || "Failed to complete trip";
      toast.error(`❌ Failed to complete trip: ${errorMessage}`);
    }
  };

  // Handle self-managed trip start
  const handleStartSelfManagedTrip = async (visit: SiteVisit) => {
    try {
      console.log("Starting self-managed trip for visit:", visit);
      const result = await updateSiteVisit({
        id: visit.id!,
        status: "In Progress",
      }).unwrap();
      console.log("Self-managed trip start result:", result);

      const transportLabel = visit.transport_type === 'self_drive' ? 'self-drive (staff vehicle)' :
                            visit.transport_type === 'own_means' ? 'own means (client vehicle)' : 'outsourced';
      toast.success(`🚗 ${transportLabel} trip started for ${visit.project}! Drive safely.`);

      // Refetch data to update the UI
      await refetchSelfManagedTrips();
    } catch (error: any) {
      console.error("Failed to start self-managed trip:", error);
      const errorMessage = error?.data?.detail || error?.message || "Failed to start trip";
      toast.error(`❌ Failed to start trip: ${errorMessage}`);
    }
  };

  // Handle self-managed trip end
  const handleEndSelfManagedTrip = async (visit: SiteVisit) => {
    try {
      console.log("Ending self-managed trip for visit:", visit);
      const result = await updateSiteVisit({
        id: visit.id!,
        status: "Completed",
      }).unwrap();
      console.log("Self-managed trip completion result:", result);

      const transportLabel = visit.transport_type === 'self_drive' ? 'self-drive (staff vehicle)' :
                            visit.transport_type === 'own_means' ? 'own means (client vehicle)' : 'outsourced';
      toast.success(`✅ ${transportLabel} trip completed for ${visit.project}! Please complete the survey when ready.`);

      // Refetch data to update the UI
      await Promise.all([
        refetchSelfManagedTrips(),
        refetchCompletedVisits(),
        refetchDriverCompletedVisits(),
        refetchExistingSurveys()
      ]);

    } catch (error: any) {
      console.error("Failed to complete self-managed trip:", error);
      const errorMessage = error?.data?.detail || error?.message || "Failed to complete trip";
      toast.error(`❌ Failed to complete trip: ${errorMessage}`);
    }
  };



  // Add getStatusVariant function
  const getStatusVariant = (status: string | null | undefined): "default" | "secondary" | "destructive" | "outline" => {
    if (!status) {
      return 'default';
    }
    switch (status.toLowerCase()) {
      case 'approved':
      case 'completed':
      case 'reviewed':
        return 'secondary';
      case 'pending':
      case 'in progress':
        return 'outline';
      case 'rejected':
      case 'cancelled':
        return 'destructive';
      default:
        return 'default';
    }
  };

  // Helper function to get transport type icon and label
  const getTransportInfo = (transportType: string, pickupLocation: string) => {
    if (transportType === 'self_drive' || pickupLocation === 'Self Drive') {
      return { icon: Key, label: 'Self Drive', color: 'text-green-600' };
    } else if (transportType === 'own_means' || pickupLocation === 'Own Means') {
      return { icon: User, label: 'Own Means', color: 'text-blue-600' };
    } else if (transportType === 'outsourced') {
      return { icon: Truck, label: 'Outsourced', color: 'text-orange-600' };
    } else {
      return { icon: Car, label: 'Company Vehicle', color: 'text-purple-600' };
    }
  };

  return (
    <>
      {/* Hide the entire "Features Completed" + progress bar */}
      <style>
        {`
          .surveyCard .space-y-1 {
            display: none !important;
          }
        `}
      </style>

      <Screen>
        <div className="space-y-8 p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
          {/* Enhanced Header */}
          <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-indigo-50">
            <CardHeader className="pb-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <Activity className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold text-gray-900">
                      Logistics Dashboard
                    </CardTitle>
                    <CardDescription className="text-gray-600 mt-1">
                      Monitor and manage all logistics operations in real-time
                    </CardDescription>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Badge variant="outline" className="text-sm">
                    {visitsLoading || vehiclesLoading
                      ? "Syncing..."
                      : "Live Data"}
                  </Badge>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Enhanced metrics with gradients */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6">
            {metrics.map((m) => (
              <Card
                key={m.title}
                className={`border-2 ${m.borderColor} shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 ${m.cardBg}`}
              >
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {m.title}
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {m.value}
                      </p>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {m.description}
                      </p>
                    </div>
                    <div
                      className={`w-12 h-12 rounded-xl flex items-center justify-center ${m.iconBg} shadow-md`}
                    >
                      <m.icon className={`w-6 h-6 ${m.iconColor}`} />
                    </div>
                  </div>
                  <div className="mt-4 flex items-center">
                    <TrendingUp
                      className={`w-4 h-4 mr-1 ${
                        m.positive ? "text-green-600" : "text-red-500"
                      }`}
                    />
                    <span
                      className={`text-sm font-medium ${
                        m.positive
                          ? "text-green-700 dark:text-green-400"
                          : "text-red-600 dark:text-red-400"
                      }`}
                    >
                      {m.change}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Quick links & special assignment */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Quick Links Card */}
            <Card className="border-0 shadow-lg bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900 dark:to-indigo-800">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-indigo-900 dark:text-indigo-100 flex items-center">
                  <Activity className="w-5 h-5 mr-2 text-indigo-600" />
                  Quick Links
                </CardTitle>
                <CardDescription className="text-indigo-700 dark:text-indigo-300">
                  Access frequently used features
                </CardDescription>
              </CardHeader>
              <CardContent>{quickLinks}</CardContent>
            </Card>

            {/* Enhanced Special Assignment Card */}
            <Card className="border-0 shadow-lg overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-black dark:from-gray-800 dark:to-gray-900 relative">
              <div className="absolute inset-0 bg-gradient-to-r from-orange-500/20 to-red-500/20"></div>
              <div className="absolute top-0 right-0 w-1/2 h-full overflow-hidden">
                <img
                  src={driverImg}
                  className="object-cover w-full h-full opacity-60"
                  alt="Driver"
                />
                <div className="absolute inset-0 bg-gradient-to-l from-black/80 via-black/40 to-transparent" />
              </div>
              <CardHeader className="relative z-10">
                <CardTitle className="text-2xl font-bold text-white flex items-center">
                  <Car className="w-6 h-6 mr-3 text-orange-400" />
                  Special Assignments
                </CardTitle>
                <CardDescription className="text-gray-300">
                  Book special transportation assignments
                </CardDescription>
              </CardHeader>
              <CardContent className="relative z-10 space-y-4">
                {/* Department Selector */}
                <div className="mb-4">
                  <Label htmlFor="department-select" className="text-sm font-medium text-gray-300 mb-2 block">
                    Filter by Department
                  </Label>
                  <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                    <SelectTrigger className="bg-white/10 border-white/20 text-white">
                      <SelectValue placeholder="Select department..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Departments</SelectItem>
                      <SelectItem value="Sales">Sales</SelectItem>
                      <SelectItem value="Marketing">Marketing</SelectItem>
                      <SelectItem value="Operations">Operations</SelectItem>
                      <SelectItem value="Finance">Finance</SelectItem>
                      <SelectItem value="HR">HR</SelectItem>
                      <SelectItem value="IT">IT</SelectItem>
                      <SelectItem value="Legal">Legal</SelectItem>
                      <SelectItem value="Customer Service">Customer Service</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-4 text-white">
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                    <div className="text-2xl font-bold text-orange-400">
                      {specialBookingStats.total}
                    </div>
                    <div className="text-sm text-gray-300">Total Bookings</div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                    <div className="text-2xl font-bold text-yellow-400">
                      {specialBookingStats.pending}
                    </div>
                    <div className="text-sm text-gray-300">Pending</div>
                  </div>
                </div>
                <div className="flex items-center justify-between pt-2">
                  {canCreateSpecialAssignment && <CreateASpecialAssignment />}
                  <div className="text-xs text-gray-400">
                    {specialBookingStats.approved} approved •{" "}
                    {specialBookingStats.inProgress} in progress
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Active Site Visits Table View */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                <MapPin className="w-5 h-5 mr-2 text-blue-600" />
                Active Site Visits
              </CardTitle>
              <CardDescription>
                Comprehensive table view of all active site visits with color-coded status
              </CardDescription>
            </CardHeader>
            <CardContent>
              {visitsLoading && (
                <div className="flex items-center justify-center py-8">
                  <Activity className="w-6 h-6 animate-spin text-gray-400 mr-2" />
                  <span className="text-gray-500">Loading visits...</span>
                </div>
              )}
              {visitsError && (
                <div className="text-center py-8">
                  <XCircle className="w-12 h-12 text-red-400 mx-auto mb-2" />
                  <p className="text-red-600">Error loading visits.</p>
                </div>
              )}
              {!visitsLoading && !visitsError && visits.length === 0 && (
                <div className="text-center py-8">
                  <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">No visits found.</p>
                </div>
              )}
              {!visitsLoading && !visitsError && visits.length > 0 && (
                <DataTable
                  data={visits}
                  columns={visitColumns}
                  enablePagination={true}
                  enableToolbar={true}
                  enableColumnFilters={true}
                  enableSorting={true}
                  containerClassName="border-0"
                  tableClassName="border-0"
                />
              )}
            </CardContent>
          </Card>

          {/* Self-Managed Trips Table */}
          {selfManagedTrips.length > 0 && (
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                  <Key className="w-5 h-5 mr-2 text-green-600" />
                  My Self-Managed Trips
                </CardTitle>
                <CardDescription>
                  Start and end trips for self-drive and own means transport
                </CardDescription>
              </CardHeader>
              <CardContent>
                {selfManagedTripsLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Activity className="w-6 h-6 animate-spin text-gray-400 mr-2" />
                    <span className="text-gray-500">Loading trips...</span>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex space-x-2">
                        <Badge variant="outline" className="text-sm">
                          {selfManagedTrips.filter(t => t.status === "Approved").length} Ready to Start
                        </Badge>
                        <Badge variant="secondary" className="text-sm">
                          {selfManagedTrips.filter(t => t.status === "In Progress").length} In Progress
                        </Badge>
                      </div>
                    </div>

                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="border-b border-gray-200 dark:border-gray-700">
                            <th className="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">Project</th>
                            <th className="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">Transport</th>
                            <th className="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">Schedule</th>
                            <th className="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">Status</th>
                            <th className="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {selfManagedTrips.map((trip) => {
                            const transportInfo = getTransportInfo(trip.transportType, trip.pickupLocation);
                            const TransportIcon = transportInfo.icon;

                            return (
                              <tr key={trip.id} className="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50">
                                <td className="py-4 px-4">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                                      <MapPin className="w-4 h-4 text-blue-600" />
                                    </div>
                                    <div>
                                      <p className="font-medium text-sm text-gray-900 dark:text-gray-100">{trip.siteName}</p>
                                      <p className="text-xs text-gray-500">{trip.pickupLocation}</p>
                                    </div>
                                  </div>
                                </td>
                                <td className="py-4 px-4">
                                  <div className="flex items-center space-x-2">
                                    <TransportIcon className={`w-4 h-4 ${transportInfo.color}`} />
                                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                      {transportInfo.label}
                                    </span>
                                  </div>
                                </td>
                                <td className="py-4 px-4">
                                  <div className="text-sm">
                                    <div className="font-medium text-gray-900 dark:text-gray-100">{trip.date}</div>
                                    <div className="text-gray-500 flex items-center">
                                      <Clock className="w-3 h-3 mr-1" />
                                      {trip.time}
                                    </div>
                                  </div>
                                </td>
                                <td className="py-4 px-4">
                                  <Badge
                                    variant={getStatusVariant(trip.status)}
                                    className="text-xs"
                                  >
                                    {trip.status}
                                  </Badge>
                                </td>
                                <td className="py-4 px-4">
                                  <div className="flex space-x-2">
                                    {trip.status === "Approved" && (
                                      <Button
                                        size="sm"
                                        onClick={() => handleStartSelfManagedTrip(trip.rawData)}
                                        className="bg-green-600 hover:bg-green-700 text-white"
                                      >
                                        <Play className="w-3 h-3 mr-1" />
                                        Start
                                      </Button>
                                    )}
                                    {trip.status === "In Progress" && (
                                      <Button
                                        size="sm"
                                        onClick={() => handleEndSelfManagedTrip(trip.rawData)}
                                        className="bg-red-600 hover:bg-red-700 text-white"
                                      >
                                        <StopCircle className="w-3 h-3 mr-1" />
                                        End
                                      </Button>
                                    )}
                                  </div>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Visits & Surveys */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* My Site Visits Summary */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                  <MapPin className="w-5 h-5 mr-2 text-blue-600" />
                  Recent Site Visits Summary
                </CardTitle>
                <CardDescription>
                  Quick overview of latest scheduled site visits
                </CardDescription>
              </CardHeader>
              <CardContent>
                {visitsLoading && (
                  <div className="flex items-center justify-center py-8">
                    <Activity className="w-6 h-6 animate-spin text-gray-400 mr-2" />
                    <span className="text-gray-500">Loading visits...</span>
                  </div>
                )}
                {visitsError && (
                  <div className="text-center py-8">
                    <XCircle className="w-12 h-12 text-red-400 mx-auto mb-2" />
                    <p className="text-red-600">Error loading visits.</p>
                  </div>
                )}
                {!visitsLoading && !visitsError && visits.length === 0 && (
                  <div className="text-center py-8">
                    <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">No visits found.</p>
                  </div>
                )}
                {!visitsLoading && !visitsError && visits.length > 0 && (
                  <div className="space-y-3">
                    {visits.slice(0, 5).map((visit) => (
                      <div key={visit.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                            <MapPin className="w-4 h-4 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-medium text-sm">{visit.siteName}</p>
                            <p className="text-xs text-gray-500">{visit.date} at {visit.time}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant={getStatusVariant(visit.status)}
                            className="text-xs"
                          >
                            {visit.status}
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigate(`/logistics/site-visits/${visit.id}`)}
                            className="h-6 w-6 p-0"
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                    {visits.length > 5 && (
                      <div className="text-center pt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => navigate('/logistics/all-site-visits')}
                        >
                          View All {visits.length} Visits
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Incomplete Surveys */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Incomplete Surveys
                </h3>
                <PrimaryButton
                  variant="outline"
                  size="sm"
                  onClick={handleRefreshSurveys}
                  disabled={isRefreshingSurveys}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className={`w-4 h-4 ${isRefreshingSurveys ? 'animate-spin' : ''}`} />
                  {isRefreshingSurveys ? 'Refreshing...' : 'Refresh'}
                </PrimaryButton>
              </div>
              {(completedVisitsLoading || existingSurveysLoading) ? (
                <div className="flex items-center justify-center py-8">
                  <Activity className="w-6 h-6 animate-spin text-gray-400 mr-2" />
                  <span className="text-gray-500">Loading surveys...</span>
                </div>
              ) : incompleteSurveys.length === 0 ? (
                <div className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg border border-green-200 dark:border-green-700">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto mb-4">
                      <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
                    </div>
                    <h4 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-2">
                      Congratulations! 🎉
                    </h4>
                    <p className="text-green-700 dark:text-green-300">
                      You have no pending site visit surveys. All your completed visits have been surveyed!
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {incompleteSurveys.map((s) => (
                    <div key={s.id} className="surveyCard">
                      <Card2
                        title={s.title}
                        description={`${s.when} — ${s.clients} client(s), pickup: ${s.pickup}`}
                        featuresCompleted={0}
                        featuresTotal={surveySteps.length}
                        badges={[{ label: `${s.clients} client(s)` }]}
                        buttonLabel={
                          <>
                            <Notebook className="mr-2 h-4 w-4" />
                            Complete Survey
                          </>
                        }
                        onButtonClick={() => startSurvey(s)}
                        progressStyle="basic"
                        progressColor="primary"
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Enhanced Complete Trips Modal */}
          <BaseModal
            isOpen={isCompleteTripsOpen}
            onOpenChange={setIsCompleteTripsOpen}
            title="🚗 Trip Management"
            description="Start and complete your assigned site visits"
            size="lg"
          >
            <div className="space-y-6">
              {(userVisitsLoading || driverAssignedVisitsLoading) ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <Activity className="w-8 h-8 animate-spin text-blue-500 mx-auto mb-3" />
                    <p className="text-gray-600">Loading your visits...</p>
                  </div>
                </div>
              ) : userVisits.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MapPin className="w-10 h-10 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    No Active Trips
                  </h3>
                  <p className="text-gray-500">
                    You don't have any approved or in-progress site visits. Completed trips have been moved to the surveys section.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      Active Site Visits ({userVisits.length})
                    </h3>
                    <div className="flex space-x-2">
                      <Badge variant="outline" className="text-sm">
                        {userVisits.filter(v => v.status === "Approved").length} Ready to Start
                      </Badge>
                      <Badge variant="secondary" className="text-sm">
                        {userVisits.filter(v => v.status === "In Progress").length} In Progress
                      </Badge>
                    </div>
                  </div>

                  {userVisits.map((visit) => (
                    <Card key={visit.id} className="p-6 hover:shadow-md transition-shadow border-l-4 border-l-blue-500">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-3">
                            <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                              <MapPin className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                                {visit.siteName}
                              </h4>
                              <p className="text-sm text-gray-500">
                                Project Site Visit
                              </p>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div className="flex items-center space-x-2">
                              <Calendar className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600">
                                {visit.date} at {visit.time}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <MapPin className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600">
                                {visit.pickupLocation}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Users className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600">
                                {visit.clients}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Car className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600">
                                {visit.chauffeur}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col items-end space-y-3">
                          <Badge
                            variant={getStatusVariant(visit.status)}
                            className="text-xs font-medium"
                          >
                            {visit.status}
                          </Badge>

                          <div className="flex space-x-2">
                            {visit.status === "Approved" && (
                              <PrimaryButton
                                variant="default"
                                size="sm"
                                onClick={() => handleStartTrip(visit.rawData)}
                                className="bg-green-600 hover:bg-green-700 text-white"
                              >
                                <Play className="w-4 h-4 mr-2" />
                                Start Trip
                              </PrimaryButton>
                            )}
                            {visit.status === "In Progress" && (
                              <PrimaryButton
                                variant="default"
                                size="sm"
                                onClick={() => handleEndTrip(visit.rawData)}
                                className="bg-red-600 hover:bg-red-700 text-white"
                              >
                                <CheckCircle className="w-4 h-4 mr-2" />
                                End Trip
                              </PrimaryButton>
                            )}
                            {visit.status === "Completed" && (
                              <PrimaryButton
                                variant="outline"
                                size="sm"
                                disabled
                                className="text-green-600 border-green-600"
                              >
                                <CheckCircle className="w-4 h-4 mr-2" />
                                Completed
                              </PrimaryButton>
                            )}
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </BaseModal>


        </div>

        {/* Modern Edit Modal */}
        <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <DialogHeader>
              <DialogTitle className="flex items-center text-xl text-gray-900 dark:text-gray-100">
                <Edit className="w-6 h-6 mr-3 text-blue-600 dark:text-blue-400" />
                Edit Site Visit
              </DialogTitle>
              <DialogDescription className="text-gray-600 dark:text-gray-400">
                Update site visit details and schedule information
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6 py-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Basic Details */}
                <div className="space-y-4">
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center">
                      <MapPin className="w-4 h-4 mr-2" />
                      Site Information
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm font-medium text-blue-700 dark:text-blue-300">
                          Site Name
                        </Label>
                        <Input
                          value={typeof editData.project === 'object'
                            ? editData.project?.name || ''
                            : String(editData.project || '')
                          }
                          onChange={(e) =>
                            setEditData({ ...editData, project: e.target.value })
                          }
                          className="mt-1 border-blue-200 dark:border-blue-700 focus:border-blue-500 dark:focus:border-blue-400"
                          placeholder="Enter site name"
                        />
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-blue-700 dark:text-blue-300">
                          Pickup Location
                        </Label>
                        <Input
                          value={editData.pickup_location || ''}
                          onChange={(e) =>
                            setEditData({
                              ...editData,
                              pickup_location: e.target.value,
                            })
                          }
                          className="mt-1 border-blue-200 dark:border-blue-700 focus:border-blue-500 dark:focus:border-blue-400"
                          placeholder="Enter pickup location"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                    <h3 className="font-semibold text-green-900 dark:text-green-100 mb-3 flex items-center">
                      <Calendar className="w-4 h-4 mr-2" />
                      Schedule
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm font-medium text-green-700 dark:text-green-300">
                          Pickup Date
                        </Label>
                        <Input
                          type="date"
                          value={editData.pickup_date || ''}
                          onChange={(e) =>
                            setEditData({ ...editData, pickup_date: e.target.value })
                          }
                          className="mt-1 border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400"
                        />
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-green-700 dark:text-green-300">
                          Pickup Time
                        </Label>
                        <Input
                          type="time"
                          value={editData.pickup_time || ''}
                          onChange={(e) =>
                            setEditData({ ...editData, pickup_time: e.target.value })
                          }
                          className="mt-1 border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Personnel & Status */}
                <div className="space-y-4">
                  <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                    <h3 className="font-semibold text-purple-900 dark:text-purple-100 mb-3 flex items-center">
                      <Users className="w-4 h-4 mr-2" />
                      Personnel
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm font-medium text-purple-700 dark:text-purple-300">
                          Marketer
                        </Label>
                        <Input
                          value={typeof editData.marketer === 'object'
                            ? editData.marketer?.fullnames || editData.marketer?.username || ''
                            : String(editData.marketer || '')
                          }
                          onChange={(e) =>
                            setEditData({ ...editData, marketer: e.target.value })
                          }
                          className="mt-1 border-purple-200 dark:border-purple-700 focus:border-purple-500 dark:focus:border-purple-400"
                          placeholder="Enter marketer name"
                        />
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-purple-700 dark:text-purple-300">
                          Driver
                        </Label>
                        <Input
                          value={typeof editData.driver === 'object'
                            ? editData.driver?.fullnames || editData.driver?.username || ''
                            : String(editData.driver || '')
                          }
                          onChange={(e) =>
                            setEditData({ ...editData, driver: e.target.value })
                          }
                          className="mt-1 border-purple-200 dark:border-purple-700 focus:border-purple-500 dark:focus:border-purple-400"
                          placeholder="Enter driver name"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
                    <h3 className="font-semibold text-orange-900 dark:text-orange-100 mb-3 flex items-center">
                      <Activity className="w-4 h-4 mr-2" />
                      Status & Notes
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm font-medium text-orange-700 dark:text-orange-300">
                          Status
                        </Label>
                        <Select
                          value={editData.status || ''}
                          onValueChange={(value) =>
                            setEditData({ ...editData, status: value })
                          }
                        >
                          <SelectTrigger className="mt-1 border-orange-200 dark:border-orange-700 focus:border-orange-500 dark:focus:border-orange-400">
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Pending">Pending</SelectItem>
                            <SelectItem value="Approved">Approved</SelectItem>
                            <SelectItem value="In Progress">In Progress</SelectItem>
                            <SelectItem value="Completed">Completed</SelectItem>
                            <SelectItem value="Cancelled">Cancelled</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-orange-700 dark:text-orange-300">
                          Remarks
                        </Label>
                        <Textarea
                          value={editData.remarks || ''}
                          onChange={(e) =>
                            setEditData({ ...editData, remarks: e.target.value })
                          }
                          className="mt-1 min-h-[80px] resize-none border-orange-200 dark:border-orange-700 focus:border-orange-500 dark:focus:border-orange-400"
                          placeholder="Add any remarks or notes..."
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                <Button
                  variant="outline"
                  onClick={() => setIsEditOpen(false)}
                  className="flex items-center space-x-2"
                >
                  <XCircle className="w-4 h-4" />
                  <span>Cancel</span>
                </Button>
                <Button
                  onClick={handleSave}
                  className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white flex items-center space-x-2"
                >
                  <CheckCircle className="w-4 h-4" />
                  <span>Save Changes</span>
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation */}
        <ConfirmModal
          isOpen={isDeleteOpen}
          onOpenChange={setIsDeleteOpen}
          variant="danger"
          title="Delete Site Visit"
          message={
            <p>
              Delete visit for <strong>{selectedVisit?.siteName}</strong>?
            </p>
          }
          confirmText="Yes, Delete"
          onConfirm={handleDeleteConfirm}
        />

        {/* Survey Modal */}
        <MultiStepModal
          isOpen={isSurveyOpen}
          onOpenChange={(open) => {
            if (!isSubmittingSurvey) {
              setSurveyOpen(open);
            }
          }}
          title={`📋 Site Visit Survey - ${currentSurvey?.title || 'Survey'}`}
          description="Complete the survey for this site visit"
          steps={surveySteps}
          onComplete={handleSubmitSurvey}
        />
      </Screen>

      {/* Debug component - remove in production */}
      {/* <LogisticsPermissionDebugger /> */}
    </>
  );
}